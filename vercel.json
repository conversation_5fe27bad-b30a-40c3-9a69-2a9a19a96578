{"version": 2, "name": "modifile", "headers": [{"source": "/(.*)", "headers": [{"key": "Cross-Origin-Embedder-Policy", "value": "require-corp"}, {"key": "Cross-Origin-Opener-Policy", "value": "same-origin"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "functions": {"app/api/**/*.js": {"maxDuration": 30}}, "regions": ["iad1"], "framework": "nextjs"}