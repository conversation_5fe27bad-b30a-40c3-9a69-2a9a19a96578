{"name": "modifile", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "build:analyze": "ANALYZE=true next build", "export": "next build && next export", "deploy": "npm run lint && npm run type-check && npm run build"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.6", "@ffmpeg/util": "^0.12.1", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@react-spring/web": "^10.0.1", "@types/node": "20.5.9", "@types/react": "^18.2.21", "@types/react-dom": "18.2.7", "@use-gesture/react": "^10.3.1", "autoprefixer": "10.4.15", "axios": "^1.5.0", "class-variance-authority": "^0.7.0", "client-only": "^0.0.1", "clsx": "^2.0.0", "eslint": "8.48.0", "eslint-config-next": "13.4.19", "framer-motion": "^12.23.0", "gsap": "^3.13.0", "lenis": "^1.3.4", "lucide-react": "^0.274.0", "next": "^15.3.5", "next-themes": "^0.2.1", "postcss": "8.4.29", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.2.3", "react-ga4": "^2.1.0", "react-icons": "^4.10.1", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2"}, "devDependencies": {"@types/gtag.js": "^0.0.13", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0"}}