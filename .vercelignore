# Development files
clear-cache.js
public/cache-buster.js
public/debug-errors.js
public/force-refresh.js
public/suppress-warnings.js

# Service worker (can cause SSR issues during build)
# public/sw.js

# Development and testing files
*.test.js
*.test.ts
*.spec.js
*.spec.ts
__tests__/
test/
tests/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out
